//@ts-nocheck
import AddIcon from '@mui/icons-material/Add'
import CloseIcon from '@mui/icons-material/Close'
import LockIcon from '@mui/icons-material/Lock'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Chip from '@mui/material/Chip'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import InputAdornment from '@mui/material/InputAdornment'
import Skeleton from '@mui/material/Skeleton'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { makeStyles, withStyles } from '@mui/styles'
import { PaymentMethod } from '@stripe/stripe-js'
import React, { useEffect, useRef, useState } from 'react'
import { toast } from 'react-toastify'

import EditPaymentMethod from './edit-payment-method'
import { MEMBERUP_PLAN_ENUM } from '@memberup/shared/src/types/enum'
import { updateMembershipSettingSuccess } from '@/memberup/store/features/membershipSlice'
import { useAppDispatch } from '@/memberup/store/hooks'
import { AppPaymentMethodNumber } from '@/shared-components/common/payment-method-number'
import { useMounted } from '@/shared-components/hooks/use-mounted'
import { formatDate } from '@/shared-libs/date-utils'
import { calculateDiscountAmount, numberToCurrency } from '@/shared-libs/numeric-utils'
import { showToast } from '@/shared-libs/toast'
import {
  getStripeCurrentPaymentMethodApi,
  getStripeGetCouponApi,
  getStripeUpcomingInvoiceApi,
  updateStripeSubscriptionApi,
} from '@/shared-services/apis/stripe.api'
import { RECURRING_INTERVAL_ENUM } from '@/shared-types/enum'

const CssTextField = withStyles({
  root: {
    '& label.Mui-focused': {
      color: 'rgba(141,148,163,0.16)',
    },
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: 'rgba(141,148,163,0.16)',
      },
      '&:hover fieldset': {
        borderColor: 'rgba(141,148,163,0.16)',
      },
      '&.Mui-focused fieldset': {
        borderColor: 'rgba(141,148,163,0.16)',
      },
    },
  },
})(TextField)

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
      maxWidth: 550,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
    '& .MuiSelect-select': {
      paddingLeft: 8,
      paddingTop: 8,
      paddingBottom: 8,
    },
    '& .MuiInputBase-root': {
      color: 'inherit',
      width: '100%',
    },
    '& .MuiOutlinedInput-root': {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.action.disabledBackground,
      },
      '&.Mui-focused': {
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: theme.palette.action.selected,
        },
      },
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: '32px 24px 16px 24px',
    textAlign: 'center',
  },
  dialogContent: {
    minHeight: 168,
    lineHeight: 1,
    padding: '0 24px 24px 24px',
  },
  divider: {
    marginTop: 24,
    marginBottom: 24,
  },
}))

const UpgradePlan: React.FC<{
  open: boolean
  selectedPlan: {
    name: MEMBERUP_PLAN_ENUM
    monthlyPrice: number
    annualPrice: number
    options?: string[]
    prorated: boolean
  }
  recurringInterval: RECURRING_INTERVAL_ENUM
  onClose: () => void
}> = ({ open, selectedPlan, recurringInterval, onClose }) => {
  const mountedRef = useMounted(true)
  const classes = useStyles()
  const dispatch = useAppDispatch()
  const [billingDetails, setBillingDetails] = useState(null)
  const [isBillingDetailsLoading, setIsBillingDetailsLoading] = useState(false)
  const [cardLast4, setCardLast4] = useState('0000')
  const [editPaymentMethod, setEditPaymentMethod] = useState(false)
  const [requestUpgrade, setRequestUpgrade] = useState(false)
  const [loadingDetails, setLoadingDetails] = useState(false)
  const [coupon, setCoupon] = useState('')
  const [couponInvalid, setCouponInvalid] = useState(false)
  const [focusCoupon, setFocusCoupon] = useState(false)
  const [couponDetails, setCouponDetails] = useState({
    id: undefined,
    name: null,
    percent_off: 0,
    amount_off: 0,
    duration: null,
  })
  const [allowAddCoupon, setAllowAddCoupon] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  const handleApplyCoupon = async () => {
    try {
      if (!coupon) {
        setCouponInvalid(true)
        return
      }
      setLoadingDetails(true)

      getStripeGetCouponApi(false, coupon)
        .then((res) => {
          if (res.data.data) {
            setCouponDetails({
              id: res.data.data.id,
              name: res.data.data.name,
              percent_off: res.data.data.percent_off,
              amount_off: res.data.data.amount_off,
              duration: res.data.data.duration,
            })
          } else {
            setCouponInvalid(true)
          }
        })
        .catch((err) => {
          setCouponInvalid(true)
        })
        .finally(() => {
          setLoadingDetails(false)
        })
    } catch (err: any) {
      setCouponInvalid(true)
    } finally {
      setLoadingDetails(false)
    }
  }

  useEffect(() => {
    const fetchUpcomingInvoice = async () => {
      setIsBillingDetailsLoading(true)

      getStripeUpcomingInvoiceApi(false, {
        plan: selectedPlan.name,
        plan_price: selectedPlan.annualPrice,
        interval: recurringInterval,
        coupon: couponDetails?.id,
      }).then((res) => {
        const resData = res.data.data
        if (resData) {
          let remainingDays = 0
          if (resData.period_end) {
            const timeDiff = Math.abs(new Date(resData.period_end * 1000).getTime() - new Date().getTime())
            remainingDays = timeDiff / (1000 * 60 * 60 * 24)
          }

          let nextDateStr
          if (selectedPlan.prorated) {
            const nextDate = new Date(resData.next_payment_attempt * 1000)
            nextDateStr = formatDate({ date: nextDate, format: 'MMMM d, yyyy' })
          } else {
            const nextDate = new Date()
            nextDate.setDate(nextDate.getDate() + remainingDays)
            nextDate.setFullYear(nextDate.getFullYear() + 1)
            nextDateStr = formatDate({ date: nextDate, format: 'MMMM d, yyyy' })
          }

          // Check if there is an existing forever coupon
          let coupon = null
          if (
            resData?.discount?.coupon?.duration === 'forever' &&
            resData?.discount?.coupon?.id !== couponDetails?.id
          ) {
            coupon = {
              id: resData?.discount?.coupon?.id,
              name: resData?.discount?.coupon?.name,
              duration: resData?.discount?.coupon?.duration,
              amount_off: resData?.discount?.coupon?.amount_off,
              percent_off: resData?.discount?.coupon?.percent_off,
            }
          } else {
            setAllowAddCoupon(true)
          }

          setBillingDetails({
            prorated: resData.prorated_amount,
            next_payment_date: nextDateStr,
            proration_date: resData.proration_date as number,
            plan_remaining_days: remainingDays,
            coupon: coupon,
          })
          setIsBillingDetailsLoading(false)
        }
      })
    }

    const fetchPaymentMethod = () => {
      getStripeCurrentPaymentMethodApi(false).then((res) => {
        const resData = res.data.data
        if (resData) {
          setCardLast4(resData.card?.last4)
          if (!resData.card?.last4 || resData.card?.last4 === '0000') {
            setEditPaymentMethod(true)
          }
        } else {
          setEditPaymentMethod(true)
        }
      })
    }

    if (open) {
      fetchUpcomingInvoice()
      fetchPaymentMethod()
    }
  }, [open, selectedPlan.name, recurringInterval, couponDetails])

  const handleSuccessPaymentMethod = (paymentMethod?: PaymentMethod) => {
    setEditPaymentMethod(false)
    setCardLast4(paymentMethod?.card?.last4)
  }

  const handleClickUpgrade = async () => {
    setRequestUpgrade(true)
    updateStripeSubscriptionApi(false, {
      plan: selectedPlan.name,
      plan_price: selectedPlan.annualPrice,
      interval: recurringInterval,
      proration_date: selectedPlan.prorated ? billingDetails?.proration_date : undefined,
      coupon: couponDetails?.id,
    })
      .then((res) => {
        if (res.data.success) {
          showToast('Payment successful', 'success')
          if (mountedRef.current) {
            const resData = res.data.data
            dispatch(
              updateMembershipSettingSuccess({
                data: {
                  stripe_subscription_id: resData['id'],
                  stripe_subscription_canceled_at: resData['canceled_at'],
                  stripe_subscription_status: resData['status'],
                  plan: resData['plan'].metadata.plan,
                  stripe_enable_annual: resData['plan'].interval === 'year',
                  stripe_subscription_period_start_at: resData['current_period_start'],
                  stripe_subscription_period_end_at: resData['current_period_end'],
                  stripe_metadata_mode: resData['metadata']?.['mode'] || null,
                },
                partialChanged: true,
              }),
            )
            onClose()
          }
        }
      })
      .catch((err) => {})
      .finally(() => {
        if (mountedRef.current) {
          setRequestUpgrade(false)
        }
      })
  }

  const totalPrice =
    recurringInterval === RECURRING_INTERVAL_ENUM.month ? selectedPlan.monthlyPrice : selectedPlan.annualPrice

  // Calculate discount amount using utility function
  let amountDiscount = 0
  if (billingDetails?.coupon) {
    amountDiscount = calculateDiscountAmount(totalPrice, billingDetails.coupon, { amountInCents: false })
  } else if (couponDetails.id) {
    amountDiscount = calculateDiscountAmount(totalPrice, couponDetails, { amountInCents: false })
  }

  let newAnnualTotal = totalPrice
  if (billingDetails?.coupon || couponDetails.duration === 'forever') {
    newAnnualTotal = totalPrice - amountDiscount
  }

  let priceSubtotal =
    recurringInterval === RECURRING_INTERVAL_ENUM.month ? selectedPlan.monthlyPrice : selectedPlan.annualPrice

  const billedToday = selectedPlan.prorated
    ? billingDetails && numberToCurrency(billingDetails.prorated / 100)
    : priceSubtotal

  return (
    <Dialog
      maxWidth="sm"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      aria-labelledby="assign-member-role-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="assign-member-role-dialog-title">
        Upgrade your plan
        <IconButton size="small" aria-label="close" className="close large color02" onClick={onClose}>
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Typography align="center" color="text.disabled" variant="body2" sx={{ mb: '32px' }}>
          You can update your payment information
        </Typography>
        <Box>
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs>
              <Typography color="text.disabled" variant="body2" gutterBottom>
                Membership Plan
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  marginBottom: '16px',
                }}
              >
                {numberToCurrency(priceSubtotal)}/{recurringInterval}
              </Typography>
            </Grid>
            <Grid item>
              <Typography
                color="text.disabled"
                variant="body2"
                sx={{ display: 'flex', alignItems: 'center', lineHeight: 1 }}
              >
                <LockIcon style={{ fontSize: 13 }} />
                &nbsp;Secured with SSL
              </Typography>
            </Grid>
          </Grid>
        </Box>

        <Divider className={classes.divider} />

        {billingDetails?.coupon && (
          <Box sx={{ marginBottom: '16px' }}>
            <Grid container alignItems="center" spacing={2}>
              <Grid item xs>
                <Grid container alignItems="center" spacing={1}>
                  <Grid item>
                    <Chip label={billingDetails?.coupon.id} />
                  </Grid>
                  <Grid item>
                    <Typography variant="body2" sx={{ ml: 2, mt: 1, fontSize: 10, color: '#AEE78B' }}>
                      {billingDetails?.coupon.percent_off
                        ? `${billingDetails?.coupon.percent_off}%`
                        : billingDetails?.coupon.amount_off && numberToCurrency(billingDetails?.coupon.amount_off)}
                      &nbsp;Off {billingDetails?.coupon.duration === 'forever' ? 'Forever' : 'Once'}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
              <Grid item>
                <Typography variant="body1">-{numberToCurrency(amountDiscount)}</Typography>
              </Grid>
            </Grid>
          </Box>
        )}

        {allowAddCoupon && (
          <Box sx={{ marginBottom: '16px' }}>
            {couponDetails.name ? (
              <Grid container alignItems="center" spacing={2}>
                <Grid item xs>
                  <Grid container alignItems="center" spacing={1}>
                    <Grid item>
                      <Chip label={couponDetails.id} />
                    </Grid>
                    <Grid item>
                      <Typography variant="body2" sx={{ ml: 2, mt: 1, fontSize: 10, color: '#AEE78B' }}>
                        {couponDetails.percent_off
                          ? `${couponDetails.percent_off}%`
                          : couponDetails.amount_off && numberToCurrency(couponDetails.amount_off)}
                        &nbsp;Off {couponDetails.duration === 'forever' ? 'Forever' : 'Once'}
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid item>
                  <Typography variant="body1">-{numberToCurrency(amountDiscount)}</Typography>
                </Grid>
              </Grid>
            ) : (
              <>
                {focusCoupon ? (
                  <CssTextField
                    size="small"
                    variant="outlined"
                    sx={{ '& .MuiInputBase-root': { borderRadius: '10px !important' } }}
                    value={coupon}
                    fullWidth
                    onChange={(e) => {
                      setCouponInvalid(false)
                      setCoupon(e.target.value)
                    }}
                    error={couponInvalid}
                    helperText={couponInvalid && 'The code is invalid'}
                    ref={inputRef}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <Button
                            type="button"
                            sx={{
                              borderTopRightRadius: '10px',
                              borderBottomRightRadius: '10px',
                            }}
                            onClick={handleApplyCoupon}
                            disabled={loadingDetails || (coupon.length || 0) < 8 || couponInvalid}
                            data-cy="checkout-coupon-apply-button"
                          >
                            Apply
                          </Button>
                        </InputAdornment>
                      ),
                      sx: {
                        paddingRight: 0,
                        width: focusCoupon ? '100%' : '40%',
                        borderRadius: '16px',
                      },
                    }}
                    data-cy="checkout-coupon-input"
                  />
                ) : (
                  <Button
                    className="no-padding"
                    startIcon={<AddIcon />}
                    onClick={() => setFocusCoupon(true)}
                    data-cy="checkout-coupon-add-button"
                  >
                    Coupon
                  </Button>
                )}
              </>
            )}
          </Box>
        )}

        <Box>
          <Typography color="text.disabled" variant="body2" gutterBottom>
            Total Billed Today
          </Typography>
          {isBillingDetailsLoading ? (
            <Skeleton height={20} variant="rounded" sx={{ width: '30%' }} />
          ) : (
            <Typography>
              <Typography component="span" variant="h6">
                {billedToday}
              </Typography>
              <Typography color="text.disabled" component="sup" variant="body2" sx={{ fontSize: 10 }}>
                &nbsp;USD
              </Typography>
            </Typography>
          )}
        </Box>

        <Divider className={classes.divider} />

        <Box>
          {editPaymentMethod ? (
            <EditPaymentMethod onCancel={() => setEditPaymentMethod(false)} onSuccess={handleSuccessPaymentMethod} />
          ) : (
            <Grid container alignItems="center" spacing={2}>
              <Grid item xs>
                <Typography color="text.disabled" variant="body2" gutterBottom>
                  Payment Details
                </Typography>
                {cardLast4 !== '0000' ? (
                  <AppPaymentMethodNumber number={cardLast4} />
                ) : (
                  <Skeleton height={21} variant="rounded" sx={{ width: '50%' }} />
                )}
              </Grid>
              <Grid item>
                <Button className="no-padding" size="small" variant="text" onClick={() => setEditPaymentMethod(true)}>
                  Edit payment
                </Button>
              </Grid>
            </Grid>
          )}
        </Box>

        <Divider className={classes.divider} />

        {!isBillingDetailsLoading && selectedPlan.prorated && billingDetails?.next_payment_date && (
          <Typography sx={{ mb: 4 }}>
            <Typography color="text.disabled" component="span" variant="body2">
              For your current billing cycle, you will be charged&nbsp;
            </Typography>
            <Typography component="span" variant="body2" fontWeight={700}>
              {billedToday}
            </Typography>
            <Typography color="text.disabled" component="span" variant="body2">
              &nbsp;(pro rated of course). When your plan renews on&nbsp;
            </Typography>
            <Typography component="span" variant="body2">
              {billingDetails.next_payment_date}
            </Typography>
            <Typography color="text.disabled" component="span" variant="body2">
              , your new&nbsp;
              {recurringInterval === RECURRING_INTERVAL_ENUM.month ? 'monthly' : 'annual'}
              &nbsp;total is&nbsp;
            </Typography>
            <Typography component="span" variant="body2" fontWeight={700}>
              {numberToCurrency(newAnnualTotal)}
            </Typography>
            <Typography color="text.disabled" component="span" variant="body2">
              , unless you cancel before then.
            </Typography>
          </Typography>
        )}

        {!isBillingDetailsLoading && !selectedPlan.prorated && billingDetails?.plan_remaining_days && (
          <Typography sx={{ mb: 4 }}>
            <Typography color="text.disabled" component="span" variant="body2">
              For your current billing cycle you will be charged&nbsp;
            </Typography>
            <Typography component="span" variant="body2" fontWeight={700}>
              {billedToday}&nbsp;
            </Typography>
            <Typography color="text.disabled" component="span" variant="body2">
              and your plan will renew on&nbsp;
            </Typography>
            <Typography component="span" variant="body2">
              {billingDetails.next_payment_date}
            </Typography>
          </Typography>
        )}

        {isBillingDetailsLoading && <Skeleton height={50} variant="rounded" sx={{ width: '100%', mb: 4 }} />}

        <Typography color="text.disabled" component="span" variant="body2">
          By clicking “Confirm and Pay” you agree to MemberUp’s annual subscription plan and to&nbsp;
          <a href="https://memberup.com/privacy-policy" target="_blank" rel="noreferrer">
            Privacy Policy
          </a>
          &nbsp; and&nbsp;
          <a href="https://memberup.com/terms-of-service" target="_blank" rel="noreferrer">
            Terms of Service
          </a>
          .
        </Typography>

        <Box sx={{ mt: '32px' }}>
          <Button
            className="round-small"
            fullWidth
            variant="contained"
            disabled={requestUpgrade || isBillingDetailsLoading}
            onClick={handleClickUpgrade}
          >
            {requestUpgrade ? <CircularProgress size={16} /> : 'Confirm and pay'}
          </Button>
        </Box>

        <Box sx={{ mt: '16px' }}>
          <Button className="round-small" color="inherit" fullWidth variant="outlined" onClick={onClose}>
            Cancel
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

UpgradePlan.displayName = 'UpgradePlan'

export default UpgradePlan
