import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeGetPromotionCodesMain } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { code } = req.query
    const result = await stripeGetPromotionCodesMain(STRIPE_SECRET_KEY, {
      code: code as string,
    })

    // Check if we found any promotion codes
    if (result.data && result.data.length > 0) {
      const promoCode = result.data[0]

      // Check if max redemptions has been reached
      if (promoCode.max_redemptions !== null && promoCode.times_redeemed >= promoCode.max_redemptions) {
        return res.status(400).send({
          errorCode: 'MAX_REDEMPTIONS_REACHED',
        })
      }

      // Check if the coupon is valid
      if (promoCode.coupon && !promoCode.coupon.valid) {
        return res.status(400).send({
          errorCode: 'COUPON_INVALID',
        })
      }
      return res.status(200).send({
        data: result.data,
      })
    } else {
      // No promotion codes found with this code
      return res.status(404).send({
        errorCode: 'NOT_FOUND',
      })
    }
  } catch (err: any) {
    console.error('Error fetching promotion code:', err)
    Sentry.captureException(err)
    res.status(500).end()
  }
})

export default handler
