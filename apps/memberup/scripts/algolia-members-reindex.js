/*

Usage examples:

# Index all members/users with accepted memberships
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js

# Index members from a specific membership/community
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js --membership_id <membership-uuid>
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js -m <membership-uuid>

# Index a specific user
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js --user_id <user-uuid>
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js -u <user-uuid>

# Show help
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js --help

Note: This script uses ES6 modules. Make sure your Node.js version supports ES modules
or that your package.json has "type": "module" configured.

*/
import 'dotenv/config'

import { PrismaClient } from '@prisma/client'
import algoliasearch from 'algoliasearch'
import { hideBin } from 'yargs/helpers'
import yargs from 'yargs/yargs'

import { confirmScriptExecution } from './common.js'

const prisma = new PrismaClient()
const algoliaClient = algoliasearch(
  process.env.NEXT_PUBLIC_ALGOLIA_APP_ID,
  process.env.NEXT_PUBLIC_ALGOLIA_ADMIN_API_KEY,
)
const algoliaIndex = algoliaClient.initIndex('member')

const getFullName = (firstName, lastName) => {
  if (!firstName && !lastName) return ''
  if (!firstName) return lastName
  if (!lastName) return firstName
  return `${firstName} ${lastName}`.trim()
}

const isUUIDv4 = (uuid) => {
  const uuidv4Pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidv4Pattern.test(uuid)
}

// Helper function to format milliseconds into MM:SS
function formatTime(ms) {
  if (ms < 0) ms = 0
  const totalSeconds = Math.floor(ms / 1000)
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// Configure yargs
const argv = yargs(hideBin(process.argv))
  .option('membership_id', {
    alias: 'm',
    type: 'string',
    description: 'Index members from a specific membership/community',
    coerce: (value) => {
      if (value && !isUUIDv4(value)) {
        throw new Error('membership_id must be a valid UUID')
      }
      return value
    },
  })
  .option('user_id', {
    alias: 'u',
    type: 'string',
    description: 'Index a specific user',
    coerce: (value) => {
      if (value && !isUUIDv4(value)) {
        throw new Error('user_id must be a valid UUID')
      }
      return value
    },
  })
  .conflicts('membership_id', 'user_id')
  .help()
  .alias('help', 'h')
  .example('$0', 'Index all members with accepted memberships')
  .example('$0 --membership_id abc123...', 'Index members from specific membership')
  .example('$0 --user_id def456...', 'Index specific user').argv

async function reindexMembers() {
  try {
    const shouldProceed = await confirmScriptExecution()

    if (!shouldProceed) return

    const { membership_id: membershipId, user_id: userId } = argv

    console.log('Starting members reindexing process...')

    let whereClause = {}
    let logMessage = ''

    if (userId) {
      // Index specific user
      whereClause = { id: userId }
      logMessage = `user with ID ${userId}`
    } else if (membershipId) {
      // Index users from specific membership
      whereClause = {
        user_memberships: {
          some: {
            membership_id: membershipId,
            status: 'accepted',
          },
        },
      }
      logMessage = `users from membership ${membershipId}`
    } else {
      // Index all users with accepted memberships
      whereClause = {
        user_memberships: {
          some: {
            status: 'accepted',
          },
        },
      }
      logMessage = 'all users with accepted memberships'
    }

    console.log(`Fetching ${logMessage} from database...`)

    const users = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        email: true,
        first_name: true,
        last_name: true,
        username: true,
        image: true,
        status: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        profile: {
          select: {
            image: true,
            image_crop_area: true,
          },
        },
        user_memberships: {
          where: {
            status: 'accepted',
          },
          select: {
            membership_id: true,
          },
        },
      },
    })

    console.log(`Fetched ${users.length} users from database.`)

    if (users.length === 0) {
      console.log('No users found matching the criteria.')
      return
    }

    // Clear existing objects only if reindexing all users
    if (!userId && !membershipId) {
      console.log('Clearing existing objects from Algolia index...')
      await algoliaIndex.clearObjects().wait()
      console.log('Cleared existing objects from Algolia index.')
    }

    console.log('Processing and uploading users to Algolia...')

    const totalUsers = users.length
    let processedCount = 0
    const startTime = Date.now()
    const batchSize = 100 // Process in batches for better performance

    for (let i = 0; i < users.length; i += batchSize) {
      const batch = users.slice(i, i + batchSize)

      // Create Algolia objects for this batch
      const algoliaObjects = batch.map((user) => {
        const membershipIds = user.user_memberships.map((um) => um.membership_id)

        return {
          objectID: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          name: getFullName(user.first_name, user.last_name),
          image: user.profile?.image || user.image,
          image_crop_area: user.profile?.image_crop_area,
          role: user.role,
          status: user.status,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          username: user.username,
          viewable_by: membershipIds,
        }
      })

      // Upload batch to Algolia
      await algoliaIndex.saveObjects(algoliaObjects).wait()
      processedCount += batch.length

      // Calculate and display progress with ETR
      const elapsedTime = Date.now() - startTime
      const averageTimePerUser = processedCount > 0 ? elapsedTime / processedCount : 0
      const remainingUsers = totalUsers - processedCount
      const estimatedTimeRemaining = processedCount > 0 ? remainingUsers * averageTimePerUser : 0

      const etrString = formatTime(estimatedTimeRemaining)
      const progressPercent = ((processedCount / totalUsers) * 100).toFixed(2)

      // Define column widths
      const progressColWidth = 30
      const batchColWidth = 25
      const etrColWidth = 15

      const progressText = `Users ${processedCount}/${totalUsers} (${progressPercent}%)`
      const batchText = `Batch ${Math.ceil(processedCount / batchSize)}/${Math.ceil(totalUsers / batchSize)}`
      const etrText = `ETR: ${etrString}`

      process.stdout.write(
        `\r${progressText.padEnd(progressColWidth)} | ${batchText.padEnd(batchColWidth)} | ${etrText.padEnd(etrColWidth)}`,
      )
    }

    if (totalUsers > 0) {
      process.stdout.write('\n') // Move to the next line after completion
    }
    console.log(`Successfully uploaded ${totalUsers} members to Algolia.`)
  } catch (error) {
    console.error('Error reindexing members:', error)
  } finally {
    await prisma.$disconnect()
    console.log('Disconnected from Prisma.')
  }
}

reindexMembers()
