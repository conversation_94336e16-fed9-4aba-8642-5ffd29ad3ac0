/*
# Index all members/users with accepted memberships
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js
# Index members from a specific membership/community
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js --membership_id <membership-uuid>

# Index a specific user
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js --user_id <user-uuid>
 */

const algoliasearch = require('algoliasearch')
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()
const algoliaClient = algoliasearch(
  process.env.NEXT_PUBLIC_ALGOLIA_APP_ID,
  process.env.NEXT_PUBLIC_ALGOLIA_ADMIN_API_KEY,
)
const algoliaIndex = algoliaClient.initIndex('member')

const getFullName = (firstName, lastName) => {
  if (!firstName && !lastName) return ''
  if (!firstName) return lastName
  if (!lastName) return firstName
  return `${firstName} ${lastName}`.trim()
}

const isUUIDv4 = (uuid) => {
  const uuidv4Pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidv4Pattern.test(uuid)
}

async function reindexMembers() {
  try {
    const args = process.argv.slice(2)
    let membershipId = null
    let userId = null

    // Parse command line arguments
    for (let i = 0; i < args.length; i++) {
      if (args[i] === '--membership_id' && args[i + 1]) {
        membershipId = args[i + 1]
        i++ // Skip next argument as it's the value
      } else if (args[i] === '--user_id' && args[i + 1]) {
        userId = args[i + 1]
        i++ // Skip next argument as it's the value
      }
    }

    console.log('Starting members reindexing process...')
    
    // Validate UUIDs if provided
    if (membershipId && !isUUIDv4(membershipId)) {
      console.error('Invalid membership_id format. Must be a valid UUID.')
      process.exit(1)
    }
    
    if (userId && !isUUIDv4(userId)) {
      console.error('Invalid user_id format. Must be a valid UUID.')
      process.exit(1)
    }

    let whereClause = {}
    let logMessage = ''

    if (userId) {
      // Index specific user
      whereClause = { id: userId }
      logMessage = `user with ID ${userId}`
    } else if (membershipId) {
      // Index users from specific membership
      whereClause = {
        user_memberships: {
          some: {
            membership_id: membershipId,
            status: 'accepted'
          }
        }
      }
      logMessage = `users from membership ${membershipId}`
    } else {
      // Index all users with accepted memberships
      whereClause = {
        user_memberships: {
          some: {
            status: 'accepted'
          }
        }
      }
      logMessage = 'all users with accepted memberships'
    }

    console.log(`Fetching ${logMessage} from database...`)
    
    const users = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        email: true,
        first_name: true,
        last_name: true,
        username: true,
        image: true,
        status: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        profile: {
          select: {
            image: true,
            image_crop_area: true,
          }
        },
        user_memberships: {
          where: {
            status: 'accepted',
          },
          select: {
            membership_id: true,
          }
        },
      },
    })

    console.log(`Fetched ${users.length} users from database.`)

    if (users.length === 0) {
      console.log('No users found matching the criteria.')
      return
    }

    // Clear existing objects only if reindexing all users
    if (!userId && !membershipId) {
      console.log('Clearing existing objects from Algolia index...')
      await algoliaIndex.clearObjects().wait()
      console.log('Cleared existing objects from Algolia index.')
    }

    console.log('Creating Algolia objects...')
    const algoliaObjects = users.map((user) => {
      const membershipIds = user.user_memberships.map((um) => um.membership_id)
      
      return {
        objectID: user.id,
        first_name: user.first_name,
        last_name: user.last_name,
        name: getFullName(user.first_name, user.last_name),
        image: user.profile?.image || user.image,
        image_crop_area: user.profile?.image_crop_area,
        role: user.role,
        status: user.status,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        username: user.username,
        viewable_by: membershipIds,
      }
    })

    console.log(`Created ${algoliaObjects.length} Algolia objects.`)

    console.log('Uploading Algolia objects...')
    await algoliaIndex.saveObjects(algoliaObjects).wait()
    console.log(`Uploaded ${algoliaObjects.length} members to Algolia.`)

  } catch (error) {
    console.error('Error reindexing members:', error)
  } finally {
    await prisma.$disconnect()
    console.log('Disconnected from Prisma.')
  }
}

reindexMembers()

/* 

Usage examples:

# Index all members/users with accepted memberships
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js

# Index members from a specific membership/community
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js --membership_id <membership-uuid>

# Index a specific user
doppler run -- node apps/memberup/scripts/algolia-members-reindex.js --user_id <user-uuid>

*/
