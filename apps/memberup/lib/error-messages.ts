export const communityFetchError = 'An error occurred while fetching the community'

export const emailInUseError = 'There is already an existing user with this email.'

export const formSubmitError = 'An error occurred, please check your connection and try again.'

export const invalidPasswordError = 'Invalid password'

export const internalServerError = 'Internal Server Error'

export const getMinImageDimensionsError = (minWidth: number, minHeight: number) =>
  `Please select an image that is at least ${minWidth} x ${minHeight} pixels.`

export const unexpectedError = 'An unexpected error occurred.'

/**
 * Translate promo code error codes to user-friendly messages
 *
 * @param errorCode - The error code from the API
 * @returns User-friendly error message
 */
export const getPromoCodeErrorMessage = (errorCode: string): string => {
    switch (errorCode) {
        case 'MAX_REDEMPTIONS_REACHED':
            return 'This promotional code has reached its maximum usage limit and is no longer available.'
        case 'COUPON_INVALID':
            return 'This promotional code is no longer valid.'
        case 'NOT_FOUND':
            return 'Promotional code not found.'
        default:
            return 'An error occurred while validating the promotional code.'
    }
}