const removeCommas = (value: string): string => {
  return `${value}`.replace(/,/g, '')
}

export const stringToNumber = (value: string, targetType = 'int') => {
  if (!value) return null

  let regexNumberMatch: any = `${removeCommas(value)}`.match(/[-+]?(\d+)(\.\d+)?/g)
  if (!regexNumberMatch) return null

  const parser = targetType === 'float' ? parseFloat : parseInt
  const num = parser(`${regexNumberMatch}`)

  return isNaN(num) ? null : num
}

export const numberToPercent = (value: number) => {
  return isNaN(value) ? null : `${value.toFixed(2)}%`
}

export const numberFormat = (value: number, minimumFractionDigits: number = 0, maximumFractionDigits: number = 0) => {
  if (!value) return ''
  return value.toLocaleString('en-US', {
    minimumFractionDigits,
    maximumFractionDigits,
  })
}

export const numberToCurrency = (
  value: number,
  minimumFractionDigits: number = 2,
  maximumFractionDigits: number = 2,
) => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    // These options are needed to round to whole numbers if that's what you want.
    //minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
    //maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
  })
  return value.toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
    // These options are needed to round to whole numbers if that's what you want.
    minimumFractionDigits, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
    maximumFractionDigits, // (causes 2500.99 to be printed as $2,501)
  })
}

/**
 * Interface for coupon/discount details
 */
export interface DiscountDetails {
  amount_off?: number // Fixed amount discount (in cents for Stripe, dollars for display)
  percent_off?: number // Percentage discount (0-100)
}

/**
 * Options for discount calculation
 */
export interface DiscountCalculationOptions {
  /** Whether amount_off is in cents (true) or dollars (false). Default: true (Stripe format) */
  amountInCents?: boolean
  /** Whether to round the result. Default: true */
  roundResult?: boolean
  /** Number of decimal places to round to. Default: 2 */
  decimalPlaces?: number
}

/**
 * Calculate discount amount based on coupon details and original price
 *
 * @param originalPrice - The original price before discount (in dollars)
 * @param discountDetails - Coupon/discount details containing amount_off or percent_off
 * @param options - Calculation options
 * @returns The discount amount in dollars
 *
 * @example
 * // Fixed amount discount (Stripe format - cents)
 * calculateDiscountAmount(100, { amount_off: 1500 }) // Returns 15.00
 *
 * // Fixed amount discount (already in dollars)
 * calculateDiscountAmount(100, { amount_off: 15 }, { amountInCents: false }) // Returns 15.00
 *
 * // Percentage discount
 * calculateDiscountAmount(100, { percent_off: 20 }) // Returns 20.00
 *
 * // No discount
 * calculateDiscountAmount(100, {}) // Returns 0
 */
export const calculateDiscountAmount = (
  originalPrice: number,
  discountDetails: DiscountDetails,
  options: DiscountCalculationOptions = {},
): number => {
  const { amountInCents = true, roundResult = true, decimalPlaces = 2 } = options

  // Validate inputs
  if (originalPrice == null || originalPrice < 0) {
    return 0
  }

  if (!discountDetails) {
    return 0
  }

  let discountAmount = 0

  // Fixed amount discount
  if (discountDetails.amount_off && discountDetails.amount_off > 0) {
    discountAmount = amountInCents
      ? discountDetails.amount_off / 100 // Convert cents to dollars
      : discountDetails.amount_off // Already in dollars
  }
  // Percentage discount
  else if (discountDetails.percent_off && discountDetails.percent_off > 0) {
    discountAmount = (discountDetails.percent_off / 100) * originalPrice
  }

  // Ensure discount doesn't exceed original price
  discountAmount = Math.min(discountAmount, originalPrice)

  // Round if requested
  if (roundResult) {
    discountAmount = Math.round(discountAmount * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces)
  }

  return Math.max(0, discountAmount) // Ensure non-negative
}

/**
 * Calculate the final price after applying discount
 *
 * @param originalPrice - The original price before discount (in dollars)
 * @param discountDetails - Coupon/discount details containing amount_off or percent_off
 * @param options - Calculation options
 * @returns The final price after discount in dollars
 *
 * @example
 * calculateFinalPrice(100, { percent_off: 20 }) // Returns 80.00
 * calculateFinalPrice(100, { amount_off: 1500 }) // Returns 85.00 (assuming amount_off in cents)
 */
export const calculateFinalPrice = (
  originalPrice: number,
  discountDetails: DiscountDetails,
  options: DiscountCalculationOptions = {},
): number => {
  const discountAmount = calculateDiscountAmount(originalPrice, discountDetails, options)
  return Math.max(0, originalPrice - discountAmount)
}
