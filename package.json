{"name": "memberup-mono", "version": "0.0.0", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "resolutions": {"import-in-the-middle": "1.13.1"}, "scripts": {"ensure-babel-config": "[ -f apps/memberup/mod.js ] && mv -n apps/memberup/mod.js apps/memberup/.babelrc.js || true", "clean": "turbo run clean && rm -rf node_modules && rm -rf packages/shared/node_modules && rm -rf packages/database/node_modules && rm -rf apps/memberup-signup/node_modules && rm -rf apps/memberup/node_modules", "rebootstrap": "rm -rf node_modules && rm -rf packages/shared/node_modules && rm -rf packages/database/node_modules && rm -rf apps/memberup-signup/node_modules && rm -rf apps/memberup/node_modules && yarn install", "build-instrumented": "yarn run ensure-babel-config && turbo run build", "build": "turbo run build", "check-types": "tsc --noEmit", "dev": "turbo run dev --parallel --continue", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md,mdx}\"", "start": "yarn turbo run dev", "start:memberup": "turbo run dev --filter='memberup'", "typecheck": "turbo typecheck", "build:memberup": "turbo run build", "run:build:memberup": "turbo run start --filter='memberup'", "start:memberup-signup": "turbo run dev --filter='memberup-signup'", "prepare": "husky", "commit": "git-cz", "generate": "turbo run db:generate", "stream-chat-seed": "cli-confirm \"Are you sure you want to run seed? This will clear out the database.\" && turbo run stream-chat-seed --filter='memberup'", "pretest": "docker compose down && docker compose up -d && turbo run db:push:test", "test": "turbo run test --no-cache --continue=always || true", "posttest": "docker compose down", "test-jest:ci": "turbo run test-jest:ci --no-cache --parallel --continue", "test-e2e": "npx checkly test", "deploy-e2e": "npx checkly deploy --force", "storybook": "turbo run storybook --filter='memberup'", "lint-staged": "lint-staged"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@commitlint/cz-commitlint": "19.8.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@memberup/eslint-config": "*", "@playwright/test": "^1.41.2", "checkly": "latest", "cli-confirm": "^1.0.1", "commitizen": "4.3.1", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "^3.4.2", "ts-node": "^10.9.2", "turbo": "^2.5.0", "typescript": "5.6.3"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "lint-staged": {"**/*.{ts,tsx,js,jsx,json,css,md,mdx}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "yarn": ">=1.22.0", "npm": "please-use-yarn"}, "packageManager": "yarn@1.22.18", "dependencies": {"@types/prettier": "^3.0.0"}}